import { ApiListResponse, ApiResponse } from "../types/api";
import type {
  InvoiceInfoItem,
  InvoiceSearchParams,
  PostPaidInvoiceItem,
  PostPaidInvoiceSearchParams,
  PrePaidInvoiceItem,
  PrePaidInvoiceSearchParams,
  PrePaidInvoiceApiRequest,
  AccountSeqSimpleInfo,
  AccountSeqDetailInfo,
  NoInvoicingItem,
  NoInvoicingSearchParams,
  PreInvoicingItem,
  PreInvoicingSearchParams,
  PreInvoicingEditData,
  PreInvoicingBatchAction,
  PreInvoicingDetailItem,
  PreInvoicingDetailSearchParams,
} from "../types/invoice";
import api from "./api";

export const getInvoiceList = async (
  params: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get("/invoice-info", { params });
  return response.data;
};

export const createInvoice = async (data: any): Promise<any> => {
  const response = await api.post("/invoice-info", data);
  return response.data;
};

export const updateInvoice = async (id: number, data: any): Promise<any> => {
  const response = await api.put(`/invoice-info/${id}`, data);
  return response.data;
};

export const getInvoiceDetail = async (
  id: number,
  account_seq_id: number,
): Promise<ApiResponse<InvoiceInfoItem>> => {
  const response = await api.get<ApiResponse<InvoiceInfoItem>>(
    `/account-seq/${account_seq_id}/invoice-info/${id}`
  );
  return response.data;
};

export interface InvoiceSimpleInfo {
  id: number;
  customer_invoice_name: string;
  customer_invoice_type: string;
}

// 获取发票信息简单列表
export const getInvocieInfoSimpleList = async (
  account_seq: string
): Promise<ApiResponse<InvoiceSimpleInfo[]>> => {
  const response = await api.get(`/invoice-info/simple-list`, {
    params: { account_seq },
  });
  return response.data;
};

// 获取分账序号的发票信息列表
export const getAccountSeqInvoiceList = async (
  account_seq_id: number,
  params?: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get(`/account-seq/${account_seq_id}/invoice-info`, { params });
  return response.data;
};

// 创建分账序号的发票信息
export const createAccountSeqInvoice = async (
  account_seq_id: number,
  data: any
): Promise<any> => {
  const response = await api.post(`/account-seq/${account_seq_id}/invoice-info`, data);
  return response.data;
};

// 获取分账序号发票信息简单列表
export const getAccountSeqInvoiceSimpleList = async (
  account_seq_id: number
): Promise<ApiResponse<InvoiceSimpleInfo[]>> => {
  const response = await api.get(`/account-seq/${account_seq_id}/invoice-info/simple-list`);
  return response.data;
};

// 调整发票信息优先级
export const adjustInvoicePriority = async (
  account_seq_id: number,
  invoice_info_id: number
): Promise<any> => {
  const response = await api.post(`/account-seq/${account_seq_id}/invoice-info/${invoice_info_id}/adjust-priority`);
  return response.data;
};

// 获取分账序号简单列表
export const getAccountSeqSimpleList = async (
  customer_num?: string
): Promise<ApiResponse<AccountSeqSimpleInfo[]>> => {
  const params = customer_num ? { customer_num } : {};
  const response = await api.get("/account-seq/simple-list", { params });
  return response.data;
};

// 获取分账序号详细信息
export const getAccountSeqDetail = async (
  account_seq: string
): Promise<ApiResponse<AccountSeqDetailInfo>> => {
  const response = await api.get(`/account-seq/${account_seq}`);
  return response.data;
};

// 获取后付费发票列表
export const getPostPaidInvoiceList = async (
  params: PostPaidInvoiceSearchParams
): Promise<ApiListResponse<PostPaidInvoiceItem[]>> => {
  const response = await api.get("/post-paid-invoice", { params });
  return response.data;
};

// 批量预开票请求数据接口
export interface BatchPreInvoiceRequest {
  customer_num: string;
  account_seq: string;
  start_charge_month: number;
  end_charge_month: number;
}

// 批量预开票API
export const batchPreInvoice = async (
  data: BatchPreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/batch-issuance", data);
  return response.data;
};

// 预开票请求数据接口
export interface PreInvoiceRequest {
  charge_detail_ids: number[];
  exchange_rate: string;
}

// 预开票API
export const preInvoice = async (
  data: PreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/issuance", data);
  return response.data;
};

// 开票记录相关接口和类型
export interface InvoiceRecordItem {
  id: number;
  task_id: string;
  invoice_type: number; // 1-预开票, 2-批量预开票
  request_info: {
    account_seq?: string;
    customer_num?: string;
    end_charge_month?: number;
    start_charge_month?: number;
    exchange_rate?: string;
    charge_detail_ids?: number[];
  };
  state: number; // 1-开票中, 2-开票完成, 3-开票失败
  reason: string | null;
  create_user: string;
  updated_at: string;
}

export interface InvoiceRecordParams {
  page?: number;
  pageSize?: number;
}

// 获取开票记录列表
export const getInvoiceRecords = async (
  params: InvoiceRecordParams
): Promise<ApiListResponse<InvoiceRecordItem[]>> => {
  const response = await api.get("/invoice-record", { params });
  return response.data;
};

// 获取预付费发票列表
export const getPrePaidInvoiceList = async (
  params: PrePaidInvoiceSearchParams
): Promise<ApiListResponse<PrePaidInvoiceItem[]>> => {
  const response = await api.get("/pre-paid-invoice", { params });
  return response.data;
};

// 提交预付费开票
export const submitPrePaidInvoice = async (
  data: PrePaidInvoiceApiRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/pre-paid-invoice/issuance", data);
  return response.data;
};

// 获取不开票列表
export const getNoInvoicingList = async (
  params: NoInvoicingSearchParams
): Promise<ApiListResponse<NoInvoicingItem[]>> => {
  const response = await api.get("/no-invoicing", { params });
  return response.data;
};

// 预开票相关接口
// 获取预开票列表
export const getPreInvoicingList = async (
  params: PreInvoicingSearchParams
): Promise<ApiListResponse<PreInvoicingItem[]>> => {
  const response = await api.get("/pre-invoicing", { params });
  return response.data;
};

// 更新预开票信息
export const updatePreInvoicing = async (
  id: number,
  data: PreInvoicingEditData
): Promise<ApiResponse<any>> => {
  const response = await api.put(`/pre-invoicing/${id}`, data);
  return response.data;
};

// 预开票批量操作
export const preInvoicingBatchAction = async (
  data: PreInvoicingBatchAction
): Promise<ApiResponse<any>> => {
  const response = await api.post("/pre-invoicing/issuance", data);
  return response.data;
};

// 导出预开票信息
export const exportPreInvoicing = async (
  params: PreInvoicingSearchParams
): Promise<Blob> => {
  // 移除空值参数
  const cleanParams = Object.keys(params).reduce((acc, key) => {
    const paramKey = key as keyof PreInvoicingSearchParams;
    const value = params[paramKey];
    if (value !== "" && value !== undefined && value !== null) {
      (acc as any)[paramKey] = value;
    }
    return acc;
  }, {} as Partial<PreInvoicingSearchParams>);

  const response = await api.get("/pre-invoicing/export", {
    params: cleanParams,
    responseType: "blob",
  });
  return response.data;
};

// 获取预开票详情列表
export const getPreInvoicingDetail = async (
  invoiceId: number,
  params: PreInvoicingDetailSearchParams
): Promise<ApiListResponse<PreInvoicingDetailItem[]>> => {
  const response = await api.get(
    `/pre-invoicing/${invoiceId}/pre-invoicing-detail`,
    { params }
  );
  return response.data;
};

// 删除预开票详情项
export const deletePreInvoicingDetail = async (
  invoiceId: number,
  detailId: number
): Promise<ApiResponse<any>> => {
  const response = await api.delete(
    `/pre-invoicing/${invoiceId}/pre-invoicing-detail/${detailId}`
  );
  return response.data;
};
