import { createApp } from "vue";
import { createPinia } from "pinia";
import "./style.css";
import "primeicons/primeicons.css";
import "material-icons/iconfont/material-icons.css";
import App from "./App.vue";
import router from "./router";
import PrimeVue from "primevue/config";
import Aura from "@primeuix/themes/aura";
import <PERSON>ton from "primevue/button";
import InputText from "primevue/inputtext";
import Password from "primevue/password";
import Card from "primevue/card";
import Toast from "primevue/toast";
import ToastService from "primevue/toastservice";
import Menu from "primevue/menu";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import Tag from "primevue/tag";
import Select from "primevue/select";
import FileUpload from "primevue/fileupload";
import Timeline from "primevue/timeline";
import Dialog from "primevue/dialog";
import Drawer from "primevue/drawer";
import FloatLabel from "primevue/floatlabel";
import Toolbar from "primevue/toolbar";
import PanelMenu from "primevue/panelmenu";
import Textarea from "primevue/textarea";
import Image from "primevue/image";
import Tree from "primevue/tree";
import TreeSelect from "primevue/treeselect";
import MultiSelect from "primevue/multiselect";
import TreeTable from "primevue/treetable";
import ToggleSwitch from "primevue/toggleswitch";
import Tooltip from "primevue/tooltip";
import DatePicker from "primevue/datepicker";
import InputNumber from "primevue/inputnumber";
import Fluid from "primevue/fluid";
import Message from "primevue/message";
import ConfirmDialog from "primevue/confirmdialog";
import Stepper from "primevue/stepper";
import StepList from "primevue/steplist";
import StepPanels from "primevue/steppanels";
import Step from "primevue/step";
import StepPanel from "primevue/steppanel";
import Tabs from "primevue/tabs";
import TabList from "primevue/tablist";
import Tab from "primevue/tab";
import TabPanels from "primevue/tabpanels";
import TabPanel from "primevue/tabpanel";
import ConfirmationService from "primevue/confirmationservice";
import ProgressSpinner from "primevue/progressspinner";
import ProgressBar from "primevue/progressbar";
import ConfirmPopup from "primevue/confirmpopup";
import Paginator from "primevue/paginator";
import SplitButton from "primevue/splitbutton";
import Divider from "primevue/divider";
import Breadcrumb from "primevue/breadcrumb";
import Badge from "primevue/badge";
import Menubar from "primevue/menubar";
import Ripple from "primevue/ripple";
import Accordion from "primevue/accordion";
import AccordionPanel from "primevue/accordionpanel";
import AccordionHeader from "primevue/accordionheader";
import AccordionContent from "primevue/accordioncontent";
import Splitter from "primevue/splitter";
import SplitterPanel from "primevue/splitterpanel";

const app = createApp(App);
const pinia = createPinia();

app.use(PrimeVue, {
  theme: {
    preset: Aura,
  },
  ripple: true,
  locale: {
    dayNames: [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六",
    ],
    dayNamesShort: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
    dayNamesMin: ["日", "一", "二", "三", "四", "五", "六"],
    monthNames: [
      "一月",
      "二月",
      "三月",
      "四月",
      "五月",
      "六月",
      "七月",
      "八月",
      "九月",
      "十月",
      "十一月",
      "十二月",
    ],
    monthNamesShort: [
      "一月",
      "二月",
      "三月",
      "四月",
      "五月",
      "六月",
      "七月",
      "八月",
      "九月",
      "十月",
      "十一月",
      "十二月",
    ],
    today: "今天",
    weekHeader: "周",
    firstDayOfWeek: 7,
  },
});
app.use(ConfirmationService);
app.use(router);
app.use(ToastService);
app.use(pinia);

// 初始化全局toast服务
import { setToastService } from "./services/toast";
import { setRouter } from "./services/router";
setToastService(app.config.globalProperties.$toast);
setRouter(router);

app.component("Button", Button);
app.component("InputText", InputText);
app.component("Password", Password);
app.component("Card", Card);
app.component("Toast", Toast);
app.component("Menu", Menu);
app.component("DataTable", DataTable);
app.component("Column", Column);
app.component("Tag", Tag);
app.component("Select", Select);
app.component("FileUpload", FileUpload);
app.component("Timeline", Timeline);
app.component("Dialog", Dialog);
app.component("Drawer", Drawer);
app.component("FloatLabel", FloatLabel);
app.component("Toolbar", Toolbar);
app.component("PanelMenu", PanelMenu);
app.component("Textarea", Textarea);
app.component("Image", Image);
app.component("Tree", Tree);
app.component("TreeSelect", TreeSelect);
app.component("MultiSelect", MultiSelect);
app.component("TreeTable", TreeTable);
app.component("ToggleSwitch", ToggleSwitch);
app.component("DatePicker", DatePicker);
app.component("InputNumber", InputNumber);
app.component("Fluid", Fluid);
app.component("Message", Message);
app.component("ConfirmDialog", ConfirmDialog);
app.component("Stepper", Stepper);
app.component("StepList", StepList);
app.component("StepPanels", StepPanels);
app.component("Step", Step);
app.component("StepPanel", StepPanel);
app.component("Tabs", Tabs);
app.component("TabList", TabList);
app.component("Tab", Tab);
app.component("TabPanels", TabPanels);
app.component("TabPanel", TabPanel);
app.component("ProgressSpinner", ProgressSpinner);
app.component("ProgressBar", ProgressBar);
app.component("ConfirmPopup", ConfirmPopup);
app.component("Paginator", Paginator);
app.component("SplitButton", SplitButton);
app.component("Divider", Divider);
app.component("Breadcrumb", Breadcrumb);
app.component("Badge", Badge);
app.component("Menubar", Menubar);
app.component("Accordion", Accordion);
app.component("AccordionPanel", AccordionPanel);
app.component("AccordionHeader", AccordionHeader);
app.component("AccordionContent", AccordionContent);
app.component("Splitter", Splitter);
app.component("SplitterPanel", SplitterPanel);
// Register the tooltip directive globally
app.directive("tooltip", Tooltip);
app.directive("ripple", Ripple);

app.mount("#app");
